import React, { useEffect, useRef, useState } from 'react';
import io from 'socket.io-client';
import { X, BarChart3 } from 'lucide-react';
import EngagementDashboard from './EngagementDashboard';

// API Configuration - Aligned with nginx proxy configuration
// /api/content/start_quiz -> http://195.35.21.98:8036
// /api/content/next_question -> http://195.35.21.98:8036
// /socketio2 -> http://195.35.21.98:8036
const FLASK_API_URL = 'https://sasthra.in';
const SOCKETIO_URL = 'https://sasthra.in';
const CAPTURE_INTERVAL_MS = 200;
const DURATION_PER_OPTION_S = 10; // Changed to match your reference (10 seconds)
const QUESTION_READING_TIME_S = 15;

const LiveQuiz = ({ quizId: inputQuizId, onClose }) => {
  const [quizId, setQuizId] = useState(inputQuizId);
  const [questionData, setQuestionData] = useState(null);
  const [quizStatus, setQuizStatus] = useState('');
  const [summary, setSummary] = useState(null);
  const [finalResults, setFinalResults] = useState(null);
  const [liveFeedLogs, setLiveFeedLogs] = useState([]);
  const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
  const [socketStatus, setSocketStatus] = useState('Disconnected');
  const [error, setError] = useState(null);
  const [centerCode] = useState(sessionStorage.getItem('centercode') || '');
  const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
  const [currentOption, setCurrentOption] = useState('');
  const [questionTimer, setQuestionTimer] = useState(null);
  const [isReadingQuestion, setIsReadingQuestion] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [questionCount, setQuestionCount] = useState(0);
  const [cameraStatus, setCameraStatus] = useState('Not initialized');

  // Manual quiz ID input states
  const [showManualInput, setShowManualInput] = useState(!inputQuizId);
  const [manualQuizId, setManualQuizId] = useState('');

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const socketRef = useRef(null);
  const videoStreamRef = useRef(null);
  const timerIntervalRef = useRef(null);

  // Initialize camera when quizId is available (like ContentQuiz.jsx)
  useEffect(() => {
    if (quizId && videoRef.current) {
      initCamera();
    }
  }, [quizId]);

  useEffect(() => {
    return () => {
      stopCamera();
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
    };
  }, []);

  const startQuiz = async () => {
    if (!inputQuizId) {
      setError('Missing Quiz ID');
      return;
    }

    try {
      setQuizStatus('Starting quiz...');

      const response = await fetch(`${FLASK_API_URL}/api/content/start_quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: inputQuizId }),
      });

      const data = await response.json();
      if (!response.ok || data.error) throw new Error(data.error || `Server error ${response.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id); // This will trigger camera initialization via useEffect
      setQuizStatus('Ready to start capture for this question.');
      setQuestionCount(1);
      setupSocketIO();
    } catch (error) {
      setError(`Error starting quiz: ${error.message}`);
    }
  };

  const handleNextQuestion = async () => {
    if (!quizId) return;
    
    try {
      await startCaptureCycle();
      setQuizStatus('Processing results and fetching next question...');

      const res = await fetch(`${FLASK_API_URL}/api/content/next_question`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizId, process_selector_id: inputQuizId })
      });
      
      const data = await res.json();

      if (data.overall_summary) {
        setFinalResults(data);
        stopCamera();
        socketRef.current?.disconnect();
        setQuizStatus('Quiz completed! View engagement dashboard for detailed analytics.');
        if (questionCount >= 5) {
          setShowDashboard(true);
        }
      } else if (data.question) {
        setQuestionData(data);
        setQuizStatus('Ready to start capture for this question.');
        setQuestionCount(prev => prev + 1);
      }
    } catch (error) {
      setError(`Error during quiz progression: ${error.message}`);
      setQuizStatus(`Error: ${error.message}`);
    }
  };

  const startCaptureCycle = async () => {
    setLiveFeedLogs([]);
    
    setIsReadingQuestion(true);
    setQuestionTimer(QUESTION_READING_TIME_S);
    setCurrentQuestionStartTime(Date.now());
    
    timerIntervalRef.current = setInterval(() => {
      setQuestionTimer(prev => {
        if (prev <= 1) {
          clearInterval(timerIntervalRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    await new Promise(resolve => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));
    
    setIsReadingQuestion(false);
    setQuestionTimer(null);
    
    const options = ['a', 'b', 'c', 'd'];
    
    for (let i = 0; i < options.length; i++) {
      setQuizStatus(`Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`);
      await processSingleOption(options[i]);
    }
    
    setQuizStatus('Capture complete for this question. Results sent.');
    
    setCurrentOptionTimer(null);
    setCurrentOption('');
  };

  const processSingleOption = (optionChar) => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let frameCount = 0;
      
      setCurrentOption(optionChar.toUpperCase());
      setCurrentOptionTimer(DURATION_PER_OPTION_S);
      
      timerIntervalRef.current = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
        setCurrentOptionTimer(remaining);
      }, 1000);
      
      const intervalId = setInterval(() => {
        const responseTime = (Date.now() - startTime) / 1000;
        captureAndSendFrame(optionChar, responseTime);
        frameCount++;
      }, CAPTURE_INTERVAL_MS);

      setTimeout(() => {
        clearInterval(intervalId);
        if (timerIntervalRef.current) {
          clearInterval(timerIntervalRef.current);
        }
        resolve();
      }, DURATION_PER_OPTION_S * 1000);
    });
  };

  const captureAndSendFrame = (optionChar, responseTime) => {
    const video = videoRef.current;
    const canvas = canvasRef.current;

    if (!video || !canvas) {
      console.warn('⚠️ No video or canvas element available');
      return;
    }

    if (!videoStreamRef.current) {
      console.warn('⚠️ No video stream available');
      return;
    }

    if (!socketRef.current?.connected) {
      console.warn('⚠️ Socket not connected');
      return;
    }

    const ctx = canvas.getContext('2d');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const frameData = canvas.toDataURL('image/jpeg', 0.6);

    console.log(`📸 Sending frame: quiz=${quizId}, option=${optionChar}, time=${responseTime.toFixed(2)}s`);

    socketRef.current.emit('process_frame', {
      quiz_id: quizId,
      frame: frameData,
      option_char: optionChar,
      response_time_seconds: responseTime
    });
  };

  const setupSocketIO = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
    
    socketRef.current = io(SOCKETIO_URL, {
      path: '/socketio2/socket.io',
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      maxReconnectionAttempts: 5
    });

    socketRef.current.on('connect', () => {
      setSocketStatus('Connected');
    });

    socketRef.current.on('disconnect', (reason) => {
      console.log('❌ Disconnected from Socket.IO server. Reason:', reason);
      setSocketStatus('Disconnected');
    });

    socketRef.current.on('connect_error', (error) => {
      console.error('❌ Socket.IO connection error:', error);
      setSocketStatus('Connection Error');
    });

    socketRef.current.on('reconnect', (attemptNumber) => {
      console.log('🔄 Reconnected to Socket.IO server. Attempt:', attemptNumber);
      setSocketStatus('Reconnected');
    });

    socketRef.current.on('reconnect_error', (error) => {
      console.error('❌ Socket.IO reconnection error:', error);
    });

    socketRef.current.on('reconnect_failed', () => {
      console.error('❌ Socket.IO reconnection failed');
      setSocketStatus('Reconnection Failed');
    });

    socketRef.current.on('hand_raised', (data) => {
      console.log('🙋 Hand raised event received:', data);
      setLiveFeedLogs((logs) => {
        const newLog = {
          ...data,
          responseTime: currentQuestionStartTime ? Math.round((new Date(data.detection_timestamp) - currentQuestionStartTime) / 1000) : 0
        };
        return [...logs, newLog];
      });
    });

    socketRef.current.onAny((eventName, ...args) => {
      console.log(`📡 Socket event received: ${eventName}`, args);
    });
  };

  const initCamera = async () => {
    if (videoStreamRef.current) return;

    try {
      console.log('🎥 Requesting camera access...');
      setCameraStatus('Requesting access...');

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoStreamRef.current = stream;
        console.log('✅ Camera initialized successfully');
        setCameraStatus('Active');
      }
    } catch (error) {
      console.error('❌ Camera error:', error);
      setCameraStatus('Error');
      setError(`Camera error: ${error.message}`);
    }
  };

  const stopCamera = () => {
    if (videoStreamRef.current) {
      videoStreamRef.current.getTracks().forEach((track) => track.stop());
      videoStreamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setCameraStatus('Not initialized');
  };

  const testSocketConnection = () => {
    if (socketRef.current) {
      socketRef.current.emit('test_connection', { message: 'Hello from client' });
    }
  };

  const handleCloseDashboard = () => {
    setShowDashboard(false);
  };

  const handleManualQuizStart = async () => {
    if (manualQuizId.trim()) {
      setError(null); // Clear any previous errors
      setShowManualInput(false);
      setQuizId(manualQuizId.trim()); // Set the quiz ID first
      // Start quiz with manual ID
      await startQuizWithId(manualQuizId.trim());
    } else {
      setError('Please enter a valid Quiz ID');
    }
  };

  const startQuizWithId = async (quizIdToUse) => {
    try {
      setQuizStatus('Starting quiz...');

      const response = await fetch(`${FLASK_API_URL}/api/content/start_quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizIdToUse }),
      });

      const data = await response.json();
      if (!response.ok || data.error) throw new Error(data.error || `Server error ${response.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id); // This will trigger camera initialization via useEffect
      setQuizStatus('Ready to start capture for this question.');
      setQuestionCount(1);
      setupSocketIO();
    } catch (error) {
      setError(`Error starting quiz: ${error.message}`);
    }
  };

  useEffect(() => {
    if (inputQuizId) {
      startQuiz();
    }
  }, [inputQuizId]);

  if (showDashboard && quizId) {
    return <EngagementDashboard quizId={quizId} onClose={handleCloseDashboard} />;
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-gray-800/95 to-gray-900/95 backdrop-blur-sm rounded-xl border border-white/10 w-full max-w-7xl max-h-[95vh] flex flex-col">
        {/* Header - Fixed */}
        <div className="flex justify-between items-center p-6 border-b border-white/10 flex-shrink-0">
          <h2 className="text-2xl font-bold text-white">Live Quiz</h2>
          <button onClick={onClose} className="text-white hover:text-blue-400 transition-colors">
            <X size={24} />
          </button>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Socket Status */}
          <div className={`p-4 rounded-lg ${
            socketStatus === 'Connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <strong>Socket Status:</strong> {socketStatus}
            {socketRef.current && (
              <button
                onClick={testSocketConnection}
                className="ml-4 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Test Connection
              </button>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="text-center text-red-400 p-4 bg-red-900/20 rounded-lg border border-red-500/30">
              <p>{error}</p>
            </div>
          )}

          {/* Manual Quiz ID Input */}
          {showManualInput && (
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-600/30">
              <h3 className="text-xl font-semibold text-white mb-4">Enter Quiz ID</h3>
              <div className="flex gap-4 items-end">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Quiz ID
                  </label>
                  <input
                    type="text"
                    value={manualQuizId}
                    onChange={(e) => setManualQuizId(e.target.value)}
                    placeholder="Enter quiz ID (e.g., 686b5c81d51a7d6958c15fdc)"
                    className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                    onKeyDown={(e) => e.key === 'Enter' && handleManualQuizStart()}
                  />
                </div>
                <button
                  onClick={handleManualQuizStart}
                  disabled={!manualQuizId.trim()}
                  className="px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-gray-500 disabled:to-gray-600 text-white rounded-lg font-semibold shadow-md transition-all duration-200 disabled:cursor-not-allowed"
                >
                  Start Quiz
                </button>
              </div>
              <p className="text-gray-400 text-sm mt-2">
                Enter the Quiz ID provided by your teacher to start the quiz.
              </p>
            </div>
          )}

          {/* Show option to switch to manual input if quiz started automatically */}
          {!showManualInput && questionData && (
            <div>
              <button
                onClick={() => {
                  setShowManualInput(true);
                  setQuestionData(null);
                  setQuizId(null);
                  setError(null);
                }}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors"
              >
                Enter Different Quiz ID
              </button>
            </div>
          )}

          {/* Quiz Content */}
          {questionData && (
            <div className="text-white space-y-6">
              {/* Status Bar */}
              <div className="status-bar p-4 bg-gray-800/50 rounded-lg border border-gray-600/30">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div><strong>Quiz ID:</strong> {quizId}</div>
                  <div><strong>Status:</strong> {quizStatus}</div>
                </div>
              </div>

              {/* Video and Live Feed Section */}
              <div className="flex flex-col lg:flex-row gap-6">
                {/* Video Section */}
                <div className="flex-1">
                  <div className="relative">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      className="w-full max-w-2xl rounded-lg border border-gray-700 bg-gray-900"
                    />
                    <div className={`absolute top-2 left-2 px-2 py-1 rounded text-sm font-medium ${
                      cameraStatus === 'Active' ? 'bg-green-600 text-white' :
                      cameraStatus === 'Error' ? 'bg-red-600 text-white' :
                      'bg-yellow-600 text-white'
                    }`}>
                      📹 Camera: {cameraStatus}
                    </div>
                  </div>
                  <canvas ref={canvasRef} className="hidden" />
                </div>

                {/* Live Feed Logs */}
                {liveFeedLogs.length > 0 && (
                  <div className="w-full lg:w-80 bg-gray-800/90 rounded-lg p-4 border border-gray-600 max-h-96 overflow-y-auto">
                    <h3 className="text-lg font-semibold mb-3 text-white">Live Hand Raises ({liveFeedLogs.length})</h3>
                    <div className="space-y-2">
                      {liveFeedLogs
                        .sort((a, b) => new Date(a.detection_timestamp) - new Date(b.detection_timestamp))
                        .map((log, idx) => (
                          <div key={idx} className="bg-gray-700/50 p-2 rounded-lg text-sm">
                            <div className="flex justify-between items-start mb-1">
                              <span className="font-medium text-white">#{idx + 1} {log.student_name}</span>
                              <span className="text-xs text-gray-300">
                                {log.responseTime}s
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-blue-300">Option {log.option.toUpperCase()}</span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Question Section */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-white">{questionData.sub_topic_name}</h2>

                {/* Reading Timer */}
                {isReadingQuestion && questionTimer !== null && (
                  <div className="bg-orange-600 text-white px-4 py-2 rounded-lg text-center font-bold animate-pulse">
                    Question Reading Time: {questionTimer}s
                  </div>
                )}

                {/* Question Text */}
                <div className="bg-gray-800/30 p-4 rounded-lg border border-gray-600/30">
                  <p className="text-lg text-white">
                    {questionData.question_number}. {questionData.question}
                  </p>
                </div>

                {/* Question Image */}
                {questionData.question_image && questionData.question_image.trim() && (
                  <div className="bg-gray-800/30 p-4 rounded-lg border border-gray-600/30">
                    <img
                      src={questionData.question_image.trim()}
                      alt="Question Image"
                      className="max-w-full h-auto rounded-lg"
                      onError={(e) => {
                        console.error('Failed to load question image:', e.target.src);
                        // Try base URL if signed URL fails
                        if (e.target.src.includes('?X-Amz-')) {
                          const baseUrl = e.target.src.split('?X-Amz-')[0];
                          console.log('Trying base URL:', baseUrl);
                          e.target.src = baseUrl;
                        } else {
                          // Hide the image container if loading fails completely
                          e.target.style.display = 'none';
                        }
                      }}
                      onLoad={() => {
                        console.log('Question image loaded successfully:', questionData.question_image);
                      }}
                    />
                  </div>
                )}

                {/* Options */}
                <div className="grid gap-3">
                  {questionData.options.map((opt, idx) => {
                    const optionLetter = String.fromCharCode(97 + idx);
                    const isCurrentOption = currentOption.toLowerCase() === optionLetter;

                    return (
                      <div key={idx} className="relative">
                        <div className={`p-4 rounded-lg border transition-all ${
                          isCurrentOption
                            ? 'bg-green-600/50 border-green-400 shadow-lg'
                            : 'bg-gray-800/30 border-gray-600/30 hover:bg-gray-700/30'
                        }`}>
                          <span className="font-medium text-white">
                            {optionLetter.toUpperCase()}. {opt}
                          </span>
                        </div>
                        {isCurrentOption && currentOptionTimer !== null && (
                          <div className="absolute top-2 right-2 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-bold animate-pulse">
                            {currentOptionTimer}s
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Final Results */}
          {finalResults && questionCount < 5 && (
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-600/30">
              <h3 className="text-lg font-semibold mb-4 text-white">Quiz Finished!</h3>
              <h4 className="text-md font-semibold mb-2 text-white">Final Raw Data:</h4>
              <pre className="bg-gray-900 p-4 rounded-lg text-gray-200 overflow-auto max-h-64 text-sm border border-gray-700">
                {JSON.stringify(finalResults, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Footer with Action Buttons - Fixed at bottom */}
        {questionData && (
          <div className="border-t border-white/10 p-6 flex-shrink-0 bg-gray-800/30">
            <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
              <div className="text-sm text-gray-300">
                Question {questionCount} • Quiz ID: {quizId}
              </div>
              <div className="flex gap-3">
                {finalResults && questionCount >= 5 && (
                  <button
                    onClick={() => setShowDashboard(true)}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg font-semibold shadow-md transition-all duration-200 flex items-center gap-2"
                  >
                    <BarChart3 size={20} />
                    View Dashboard
                  </button>
                )}
                <button
                  onClick={handleNextQuestion}
                  className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-gray-500 disabled:to-gray-600 text-white rounded-lg font-semibold shadow-md transition-all duration-200 disabled:cursor-not-allowed"
                  disabled={quizStatus.includes('Capturing') || quizStatus.includes('Processing') || isReadingQuestion}
                >
                  {finalResults ? 'Quiz Complete' : 'Start Capture & Next Question'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LiveQuiz;
